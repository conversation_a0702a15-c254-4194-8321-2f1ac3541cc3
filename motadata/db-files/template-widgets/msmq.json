[{"_type": "0", "id": 10000000000493, "visualization.name": "Today's Availability", "visualization.category": "Custom", "visualization.type": "Application Availability", "visualization.timeline": {"relative.timeline": "today"}, "visualization.result.by": ["monitor"], "visualization.data.sources": [{"visualization.result.by": ["monitor"], "type": "availability", "join.type": "all", "join.result": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "Monitor", "entities": []}]}, {"visualization.result.by": ["monitor"], "type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~downtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~suspendtime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unknowntime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~maintenancetime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~disabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}, {"data.point": "system.process~unreachabletime.seconds", "aggregator": "sum", "entity.type": "Monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "axis.titles": {}, "highchart.settings": {"plotOptions": {"pie": {"innerSize": "70%"}}}}}, "join.type": "all", "join.columns": ["monitor"], "entity.type": "Monitor", "container.type": "Template", "entities": []}, {"_type": "0", "id": 10000000000494, "visualization.name": "Availability Statistics", "visualization.category": "Grid", "visualization.type": "Application Availability Time Series", "visualization.timeline": {"relative.timeline": "today"}, "visualization.data.sources": [{"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-1d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-7d"}}, {"type": "availability", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "system.process~uptime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~downtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unreachabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~maintenancetime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~suspendtime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~disabletime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "system.process~unknowntime.percent", "aggregator": "avg", "entity.type": "monitor", "entities": []}], "visualization.timeline": {"relative.timeline": "-15d"}}], "visualization.properties": {"chart": {"rotation.angle": 0, "chart.legend": "yes", "chart.label": "no", "axis.titles": {}, "highchart.settings": {}, "sorting": {"limit": 10, "order": "desc", "column": "system.process~uptime.percent.avg"}}}}, {"id": 10000000000495, "_type": "0", "visualization.name": "Total Sessions", "visualization.description": "MSMQ Total Sessions", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "msmq.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "msmq.sessions", "icon": {"name": "sessions", "placement": "prefix"}}, "header": {"title": "Total Sessions", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "msmq.sessions.last"}]}}}}, {"id": 10000000000496, "_type": "0", "visualization.name": "IP Session", "visualization.description": "MSMQ IP Session", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.ip.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "msmq.ip.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "msmq.ip.sessions", "icon": {"name": "ip", "placement": "prefix"}}, "header": {"title": "IP Session", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "msmq.ip.sessions.last"}]}}}}, {"id": 10000000000497, "_type": "0", "visualization.name": "Outgoing HTTP Session", "visualization.description": "MSMQ Outgoing HTTP Session", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.outgoing.http.sessions", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "msmq.outgoing.http.sessions"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "msmq.outgoing.http.sessions", "icon": {"name": "outgoing-session", "placement": "prefix"}}, "header": {"title": "Outgoing HTTP Sessions", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "msmq.outgoing.http.sessions.last"}]}}}}, {"id": 10000000000498, "_type": "0", "visualization.name": "Incoming Messages", "visualization.description": "MSMQ Incoming Messages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.incoming.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "msmq.incoming.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "msmq.incoming.messages", "icon": {"name": "incoming-messages", "placement": "prefix"}}, "header": {"title": "Incoming Messages", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "msmq.incoming.messages.last"}]}}}}, {"id": 10000000000499, "_type": "0", "visualization.name": "Outgoing Messages", "visualization.description": "MSMQ Outgoing Messages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.outgoing.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "msmq.outgoing.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "msmq.outgoing.messages", "icon": {"name": "outgoing-messages", "placement": "prefix"}}, "header": {"title": "Outgoing Messages", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "msmq.outgoing.messages.last"}]}}}}, {"id": 10000000000500, "_type": "0", "visualization.name": "Pending Messages", "visualization.description": "MSMQ Pending Messages", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "KPI Gauge", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.pending.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}], "correlated.data.points": [{"type": "metric", "data.point": "msmq.pending.messages"}]}], "visualization.properties": {"gauge": {"style": {"type": "number", "font.size": "medium", "color.data.point": "msmq.pending.messages", "icon": {"name": "pending-messages", "placement": "prefix"}}, "header": {"title": "Pending Messages", "style": {"font.size": "medium"}}, "footer": {"title": "", "style": {"font.size": "large"}, "data.points": [{"label": "", "value": "msmq.pending.messages.last"}]}}}}, {"id": 10000000000501, "_type": "0", "visualization.name": "Queue Details", "visualization.description": "MSMQ Queue Details", "visualization.timeline": {"relative.timeline": "-3h"}, "visualization.category": "Grid", "visualization.type": "Grid", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.queue~journal.size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "msmq.queue~pending.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "msmq.queue~journal.pending.messages", "aggregator": "last", "entity.type": "monitor", "entities": []}, {"data.point": "msmq.queue~size.bytes", "aggregator": "last", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"grid": {"searchable": "yes", "column.selection": "no", "header": "yes", "style": {"header.font.size": "small"}, "columns": [{"name": "monitor", "show": "no", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "msmq.queue", "title": "msmq.queue", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "msmq.queue~journal.size.bytes.last", "title": "journal.size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "msmq.queue~pending.messages.last", "title": "Pending", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "msmq.queue~journal.pending.messages.last", "title": "journal Pending", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}, {"name": "msmq.queue~size.bytes.last", "title": "size", "show": "yes", "sortable": "yes", "disable": "no", "resizable": "yes", "selectable": "yes", "orderable": "yes", "style": {}}]}}}, {"id": 10000000000502, "_type": "0", "visualization.name": "MSMQ Sessions", "visualization.description": "MSMQ MSMQ Sessions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000503, "_type": "0", "visualization.name": "Multicast Session Details", "visualization.description": "MSMQ Multicast Session Details", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.incoming.multicast.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}, {"data.point": "msmq.outgoing.multicast.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "yes", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000504, "_type": "0", "visualization.name": "Incoming Messages", "visualization.description": "MSMQ Incoming Messages", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.incoming.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000505, "_type": "0", "visualization.name": "MSMQ IP Sessions", "visualization.description": "MSMQ MSMQ IP Sessions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.ip.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000506, "_type": "0", "visualization.name": "Incoming Messages Per Second", "visualization.description": "MSMQ Incoming Messages Per Second", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.incoming.messages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000507, "_type": "0", "visualization.name": "Outgoing Messages", "visualization.description": "MSMQ Outgoing Messages", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.outgoing.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000508, "_type": "0", "visualization.name": "Outgoing HTTP Sessions", "visualization.description": "MSMQ Outgoing HTTP Sessions", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.outgoing.http.sessions", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000509, "_type": "0", "visualization.name": "Outgoing Message Per Second", "visualization.description": "MSMQ Outgoing Message Per Second", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.outgoing.messages.per.sec", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}, {"id": 10000000000510, "_type": "0", "visualization.name": "Pending Messages", "visualization.description": "MSMQ Pending Messages", "visualization.timeline": {"relative.timeline": "today"}, "visualization.category": "Chart", "visualization.type": "Line", "visualization.data.sources": [{"type": "metric", "filters": {"data.filter": {}, "result.filter": {}}, "data.points": [{"data.point": "msmq.pending.messages", "aggregator": "avg", "entity.type": "monitor", "entities": []}]}], "visualization.properties": {"chart": {"chart.legend": "no", "chart.label": "no", "highchart.settings": {}}}}]